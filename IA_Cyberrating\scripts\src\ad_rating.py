import pandas as pd
import numpy as np
import logging.handlers
from datetime import date
from sqlalchemy import text
from .utils import db_connexion


class ADrating:
    """
    The ADrating class allows through its different methods to manage:
            - Import the necessary data
            - Compute the rating
            - Export the results
    Atributs:
        
    """
    def __init__(self):
        self.my_logger = logging.getLogger("MyLogger")
        self.engine = db_connexion()
        self.ad_audit = None
        self.last_audit = None
        self.ad_domain_info = None
        self.ad_domain = None
        self.ad_grouping = None
        self.ad_setting = None
        self.ad_setting_exlusion = None
        self.ad_rating = None
        self.import_data()
        self.define_weigths()
        self.clean_data()
        self.apply_weigths_by_criticity()
        self.groupings_rating()
        self.domains_rating()
        self.concat_ad_rating()
        self.preprocess_for_export()
        self.delete_today()
        self.export_append_tables_ad(self.ad_rating)

    def get_ad_audit(self) -> None:
        """
        Attributes the dataframe of AD information database
        id / date / value / details / criticality / domain_id /
        grouping_id / setting_id / threat_id / upload_history_id /
        vulnerability_id
        """

        ad_audit = pd.read_sql("SELECT * FROM active_directory_pingcastleauditresult", self.engine)
        self.ad_audit = ad_audit

    def get_ad_domain_info(self) -> None:
        """
        Attributes the dataframe of domain info
        id / date / valuedetails / domain_id / grouping_id /
        setting_id / upload_history_id
        """

        ad_domain_info = pd.read_sql("SELECT * FROM active_directory_addomaininfo", self.engine)

        self.ad_domain_info = ad_domain_info

    def get_ad_domain(self) -> None:
        """
        Attributes the dataframe of ad_domain
        id / name / cluster_id / division_id / subsidiary_id / last_audit_date
        """

        ad_domain = pd.read_sql("SELECT * FROM active_directory_addomain", self.engine)

        self.ad_domain = ad_domain

    def get_ad_grouping(self) -> None:
        """
        Attributes the dataframe of referencial ad grouping
        id / name
        """

        ad_grouping = pd.read_sql("SELECT * FROM active_directory_pingcastlegrouping", self.engine)
        self.ad_grouping = ad_grouping[ad_grouping["weight"] != 0]

    def get_ad_setting(self) -> None:
        """
        Attributes the dataframe of setting
        id / name / source / grouping_id / timeline_display
        """

        ad_setting = pd.read_sql("SELECT * FROM active_directory_pingcastlesetting", self.engine)
        self.ad_setting = ad_setting

    def get_ad_setting_exlusion(self) -> None:
        """
        Attributes the dataframe of setting
        id / limit_date / setting_id
        """

        ad_setting_ex = pd.read_sql("SELECT * FROM active_directory_settingexclusionfromrating", self.engine)
        self.ad_setting_exclusion = ad_setting_ex

    def import_data(self) -> None:
        """
        Runs all import methods
        """
        self.get_ad_audit()
        self.get_ad_domain_info()
        self.get_ad_domain()
        self.get_ad_grouping()
        self.get_ad_setting()
        self.get_ad_setting_exlusion()

    def define_weigths(self) -> None:
        """
        Define setting weights:
            - remove settings to be excluded
            - integrate critical setting weights
            - calculate unit weights of settings included in "the rest".
        """
        # Exclude setting from setting_exclusion table
        self.ad_setting = self.ad_setting[~self.ad_setting["id"].isin(self.ad_setting_exclusion["setting_id"])]
        self.ad_setting["fixed_weight"] = self.ad_setting[self.ad_setting["weight"] != 0]["weight"]
        # Setting count by grouping
        gb = self.ad_setting[["grouping_id", "id"]].groupby(["grouping_id"]).count()
        gb["sum_fixed"] = self.ad_setting[["grouping_id", "weight"]].groupby(["grouping_id"]).sum()
        gb["nb_fixed_weight"] = self.ad_setting[["grouping_id", "fixed_weight"]].groupby(["grouping_id"]).count()
        gb.rename(columns={"id": "count"}, inplace=True)
        # count of settings without fixed_weight
        gb["count_without_critical"] = gb["count"] - gb["nb_fixed_weight"]
        # define the rest total weigth
        gb["rest_weight_total"] = np.maximum(0, 100 - gb["sum_fixed"])
        # define unitaire rest weigth
        gb["rest_weight"] = gb["rest_weight_total"] / np.log1p(3**3) / gb["count_without_critical"]
        self.rest_weight = gb["rest_weight"]

    def clean_data(self) -> None:
        """
        Preprocess and merges some dataframes:
            - Selection of results from last audit
            - Selection of settings with a critical rating other than 0
        """
        # Last_audit : Extract last results from audit for each key (domain / grouping / setting)
        self.last_audit = self.ad_audit.sort_values('date').groupby(['domain_id', 'grouping_id', 'setting_id'], as_index=False).last()
        # The ‘grouping_id’ column in the audit results is replaced by the repository values associated with the settings.
        self.last_audit = self.last_audit.drop(columns=["grouping_id"]).merge(
            self.ad_setting[["id", "grouping_id"]], left_on="setting_id",
            right_on="id", how="right").drop(columns=['id_y']).rename(columns={"id_x": "id"})
        # Aplly a filter on criticality (exclude good tag: criticality == 1 & information: criticality == 0 )
        self.last_audit_bad = self.last_audit[~self.last_audit["criticality"].isin([0, 1])][["domain_id", "grouping_id", "setting_id", "criticality"]]

    def apply_weigths_by_criticity(self) -> None:
        """
        Calculation of penalties by settings, first naive approach:
        - for bad in the rest:
        penalty = unit_rest_weight * log( 1 + criticality**criticality)
        - for bad with fixed weight:
        penalty = fixed weight
        - for good:
        penalty = O
        -for investigate:
        penalty = unit_rest_weight * log( 1 + 3**3)
        """
        # The results of the last audit are merged with the weights previously defined for the settings of each grouping.
        merge_sr = pd.merge(self.last_audit_bad, self.rest_weight, on='grouping_id', how="inner")
        merge_sr = pd.merge(merge_sr, self.ad_setting[["id", "weight"]], left_on="setting_id", right_on="id", how="left")
        merge_sr = merge_sr.drop(columns="id", axis=1)
        merge_sr.loc[merge_sr["weight"].isna(), "weight"] = 0
        # Calculation of penalties by settings
        setting_bad = merge_sr[merge_sr["criticality"] != 6]
        # on sépare les settings aynat des poids fixes de ceux appartenant à the rest
        setting_bad_fixed = setting_bad[setting_bad["weight"] != 0]
        setting_bad_rest = setting_bad[setting_bad["weight"] == 0]
        # penalité pour les setting ayant un poids fixé
        setting_bad_fixed = setting_bad_fixed.copy()
        setting_bad_fixed["penalty"] = setting_bad_fixed["weight"]
        # calcul des pénalité pour les settings dans the rest
        setting_bad_rest = setting_bad_rest.copy()
        setting_bad_rest["penalty"] = setting_bad_rest["rest_weight"] * setting_bad_rest['criticality'].apply(lambda x: np.log1p(x**x))
        setting_bad = pd.concat([setting_bad_fixed, setting_bad_rest], ignore_index=True).reset_index()
        setting_bad.drop(columns=["level_0"], axis=1, inplace=True, errors="ignore")
        # define Ad_level as setting
        setting_bad["setting_tag"] = 0  # "bad"
        # Ajout des settings tag good
        setting_good = self.last_audit[self.last_audit["criticality"] == 1][["domain_id", "grouping_id", "setting_id", "criticality"]]
        setting_good["setting_tag"] = 1  # "good"
        setting_good["penalty"] = 0
        # Ajout des settings tag investiguate -> dans la notation
        setting_inv = merge_sr[merge_sr["criticality"] == 6].copy()
        setting_inv.loc[:, "setting_tag"] = 2  # "investigate"
        setting_inv["penalty"] = setting_inv["rest_weight"] * np.log1p(3**3)
        # On réunit les différents cas dans un seul data frame
        sr = pd.concat([setting_bad, setting_good, setting_inv], ignore_index=True).reset_index()
        sr["ad_level"] = 0  # "setting"
        sr.drop(columns=["rest_weight", "level_0", "weight"], axis=1, inplace=True, errors="ignore")
        sr["rating"] = -1
        self.setting_rating = sr

    def groupings_rating(self) -> None:
        """
        Calculation of rating by grouping, aggregation of penalties associated with settings by grouping:
        gr = max(0, 100 - sum(penalty))
        """
        # calculating the sum of penalties by groupings
        gr = self.setting_rating.groupby(["domain_id", 'grouping_id'])['penalty'].sum().reset_index()
        # Calculation of rating by grouping
        gr["rating"] = gr['penalty'].apply(lambda x: max(0, 100 - x))
        # define ad_level as grouping
        gr["ad_level"] = 1  # "grouping"
        self.grouping_rating = gr

    @staticmethod
    def wa(rating, weights):
        """_Defines the weighted average method_

        Args:
            rating (_pd.Series_): _domain grouping_rating values_
            weights (_pd.Series_): _grouping weights_

        Returns:
            _float_: _domain rating_
        """
        return np.average(rating, weights=(weights))

    def domains_rating(self) -> None:
        """
        Calculation of rating by domain, aggregation of grouping rating associated with grouping weigths by domain:
        dr = wa(grouping_rating, grouping_weights)
        """
        # Grouping weights are merged with calculated grouping_rating
        dr = pd.merge(self.grouping_rating,
                      self.ad_grouping[["id", "weight"]],
                      left_on='grouping_id', right_on="id", how="inner")
        # Calculation of rating by domain
        dr = dr.groupby("domain_id").apply(lambda x: pd.Series({'rating': self.wa(x["rating"], x["weight"])}))["rating"].reset_index()
        # define ad_level as domain
        dr["ad_level"] = 2  # "domain"
        self.domain_rating = dr

    def concat_ad_rating(self) -> None:
        """
        Concatenation of results:
            - setting penalties
            - grouping rating
            - domain rating
        """
        ad_rating = pd.concat([self.setting_rating, self.grouping_rating, self.domain_rating], ignore_index=True).reset_index()
        self.ad_rating = ad_rating

    def preprocess_for_export(self) -> None:
        """_Added a timestamp column to set the current date to the notes
            We remove the columns that are not necessary for exporting the data_
        """
        self.ad_rating.insert(0, "timestamp", date.today())
        self.ad_rating.drop(columns=['level_0', "index"], axis=1, inplace=True, errors="ignore")

    def merge_id_name(self) -> None:
        """
        Attributes the type of ip, the rating duration, the threshold for vulnerable ip,
        the list of subsidiary to remove and the rating for healthy ips
        """
        # domain names
        self.ad_rating = pd.merge(self.ad_rating, self.ad_domain[["id", "name"]], left_on='domain_id', right_on="id", how='left')
        self.ad_rating.drop(columns=["id"], axis=1, inplace=True)
        self.ad_rating.rename(columns={"name": "domain_name"}, inplace=True)
        # grouping names
        self.ad_rating = pd.merge(self.ad_rating, self.ad_grouping[["id", "name"]], left_on='grouping_id', right_on="id", how='left')
        self.ad_rating.drop(columns=["id"], axis=1, inplace=True)
        self.ad_rating.rename(columns={"name": "grouping_name"}, inplace=True)
        # setting names
        self.ad_rating = pd.merge(self.ad_rating, self.ad_setting[["id", "name"]], left_on='setting_id', right_on="id", how='left')
        self.ad_rating.drop(columns=["id"], axis=1, inplace=True)
        self.ad_rating.rename(columns={"name": "setting_name"}, inplace=True)

    def delete_today(self) -> None:
        """
        Deletes today data for rating tables if it exists
        """

        today = date.today()
        table = "cyber_rating_orangecyberratingactivedirectory"

        max_date = pd.read_sql("SELECT MAX(timestamp) FROM {}".format(table), self.engine)
        max_date = max_date.loc[0, "MAX(timestamp)"]

        if today == max_date:
            with self.engine.connect() as conn:
                conn.execute(text("DELETE FROM {} WHERE timestamp = '{}'".format(table, today)))
                conn.commit()

    def export_append_tables_ad(self, df: pd.DataFrame) -> None:
        """
        Exports ad rating table

        Parameters
        ----------
        df : DataFrame
            The dataframe to export
        table_name: str
            The name of the table
        """
        df.to_sql("cyber_rating_orangecyberratingactivedirectory", self.engine, if_exists="append", index=False)


if __name__ == "__main__":
    data = ADrating()
    data.ad_rating.to_csv("dta_ex.csv")
    # print(data.ad_rating)
