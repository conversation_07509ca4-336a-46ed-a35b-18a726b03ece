import pandas as pd
import numpy as np
from datetime import date
from sqlalchemy import text
from .utils import db_connexion
import logging


class DataExport:
    """
    A class used to export all the datas

    Attributes
    ----------
    engine : Engine
        a MYSQL engine connection

    Methods
    -------
    delete_today()
        Deletes today data for rating tables if it exists
    rename_data_tables(category, family, total, findings, findings_assets, patching_cadence)
        Renames some columns for rating tables
    export_append_tables(df, table_name)
        Exports a rating table
    export_replace_tables(df, table_name)
        Exports a daily table
    rating_tables_export(category, family, total, findings, findings_assets, data_pc)
        Runs all functions to export rating tables
    rename_correction_tables
        Renames some columns for correction tables
    correction_tables_export
        Runs all functions to export correction tables
    """

    def __init__(self):
        self.engine = db_connexion()

    def delete_today(self) -> None:
        """
        Deletes today data for rating tables if it exists
        """

        today = date.today()
        tables_list = [
            "cyber_rating_orangecyberratinggradecategory",
            "cyber_rating_orangecyberratinggradefamily",
            "cyber_rating_orangecyberratinggrade"
        ]

        for table in tables_list:
            max_date = pd.read_sql("SELECT MAX(timestamp) FROM {}".format(table), self.engine)
            max_date = max_date.loc[0, "MAX(timestamp)"]

            if today == max_date:
                with self.engine.connect() as conn:
                    conn.execute(text("DELETE FROM {} WHERE timestamp = '{}'".format(table, today)))
                    conn.commit()

    @staticmethod
    def rename_data_tables(
            category: pd.DataFrame,
            family: pd.DataFrame,
            total: pd.DataFrame,
            findings: pd.DataFrame,
            findings_assets: pd.DataFrame,
            findings_fqdn: pd.DataFrame,
            findings_assets_fqdn: pd.DataFrame,
            pc_history: pd.DataFrame,
            pc_history_fqdn: pd.DataFrame,
            severity_pc_mean: pd.DataFrame) -> None:
        """
        Renames some columns for rating tables

        Parameters
        ----------
        category : DataFrame
            The category rating dataframe
        family: DataFrame
            The family rating dataframe
        total : DataFrame
            The total rating dataframe
        findings : DataFrame
            The dataframe with all scans for entities since 45 days about ip
        findings_assets : DataFrame
            The dataframe with all scans for assets since 45 days about ip
        findings_fqdn : DataFrame
            The dataframe with all scans for entities since 45 days about fqdn
        findings_assets_fqdn : DataFrame
            The dataframe with all scans for assets since 45 days about fqdn
        patching_history : DataFrame
            The dataframe with all scans and information about ip
        patching_history_fqdn : DataFrame
            The dataframe with all scans and information about fqdn
        """

        category.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        family.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        total.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        findings.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id"},
            inplace=True)
        
        findings_fqdn.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id"},
            inplace=True)

        findings_assets.rename(columns={"asset": "asset_id"}, inplace=True)
        # # TODO : Add in DB
        # L'ignore errors permet de gérer les dataframes vides
        findings_assets.drop("group", axis=1, inplace=True, errors='ignore')
        findings_assets_fqdn.rename(columns={"asset": "asset_id"}, inplace=True)
        # # TODO : Add in DB
        findings_assets_fqdn.drop("group", axis=1, inplace=True, errors='ignore')

        pc_history.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "severity": "severity_id"},
            inplace=True)
        pc_history = pc_history.fillna('')

        pc_history_fqdn.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "severity": "severity_id"},
            inplace=True)
        pc_history_fqdn = pc_history_fqdn.fillna('')

        severity_pc_mean.rename(
            columns={"division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id"},
            inplace=True)
        severity_pc_mean = severity_pc_mean.fillna('')

    def export_append_tables(self, df: pd.DataFrame, table_name: str) -> None:
        """
        Exports a rating table

        Parameters
        ----------
        df : DataFrame
            The dataframe to export
        table_name: str
            The name of the table
        """

        df = df[~((df.cluster_id >= 1000) & (df.level == "cluster"))]
        df = df.copy()
        df["cluster_id"] = [np.nan if i > 999 else i for i in df.cluster_id]
        df.to_sql(table_name, self.engine, if_exists="append", index=False)

    def export_replace_tables(self, df, table_name):
        """
        Exports a daily table

        Parameters
        ----------
        df : DataFrame
            The dataframe to export
        table_name: str
            The name of the table
        """
        try:
            with self.engine.connect() as conn:
                # Clear existing data first
                conn.execute(text("DELETE FROM {}".format(table_name)))
                conn.commit()
                
                # Insert new data
                if not df.empty:
                    df.to_sql(table_name, con=conn, if_exists="append", index=False)
                    conn.commit()
                
        except Exception as e:
            print(f"Error exporting to {table_name}: {str(e)}")
            # Log the error but don't fail the entire process
            raise e
        finally:
            if 'conn' in locals():
                conn.close()

    def rating_tables_export(
            self,
            category: pd.DataFrame,
            family: pd.DataFrame,
            total: pd.DataFrame,
            findings: pd.DataFrame,
            findings_assets: pd.DataFrame,
            findings_fqdn: pd.DataFrame,
            findings_assets_fqdn: pd.DataFrame,
            pc_history: pd.DataFrame,
            pc_history_fqdn: pd.DataFrame,
            severity_pc_mean: pd.DataFrame,
            ) -> None:
        """
        Runs all functions to export rating tables

        Parameters
        ----------
        category : DataFrame
            The category rating dataframe
        family: DataFrame
            The family rating dataframe
        total : DataFrame
            The total rating dataframe
        findings : DataFrame
            The dataframe with all scans for entities since 45 days about ip
        findings_assets : DataFrame
            The dataframe with all scans for assets since 45 days about ip
        findings : DataFrame
            The dataframe with all scans for entities since 45 days about fqdn
        findings_assets : DataFrame
            The dataframe with all scans for assets since 45 days about fqdn
        patching_history : DataFrame
            The dataframe with all scans and information about ip
        patching_history_fqdn : DataFrame
            The dataframe with all scans and information about fqdn
        severity_pc_mean:
            The dataframe about mean correction delay by severity
        """
        my_logger = logging.getLogger("MyLogger")

        my_logger.info("[rating_tables_export] Début de l'export des tables de rating.")
        try:
            my_logger.info("Deleting today's data if it exists.")
            self.delete_today()

            my_logger.info("Renaming columns of DataFrames.")
            self.rename_data_tables(category, family, total, findings, findings_assets,
                                    findings_fqdn, findings_assets_fqdn, pc_history,
                                    pc_history_fqdn, severity_pc_mean)

            my_logger.info(f"Appending : category ({len(category)}) -> cyber_rating_orangecyberratinggradecategory")
            self.export_append_tables(category, "cyber_rating_orangecyberratinggradecategory")
            my_logger.info(f"Appending : family ({len(family)}) -> cyber_rating_orangecyberratinggradefamily")
            self.export_append_tables(family, "cyber_rating_orangecyberratinggradefamily")
            my_logger.info(f"Appending : total ({len(total)}) -> cyber_rating_orangecyberratinggrade")
            self.export_append_tables(total, "cyber_rating_orangecyberratinggrade")

            my_logger.info(f"Transforming cluster_id on findings ({len(findings)}) and findings_fqdn ({len(findings_fqdn)})")
            if 'cluster_id' in findings.columns:
                findings["cluster_id"] = [np.nan if i > 999 else i for i in findings.cluster_id]
            else:
                findings["cluster_id"] = np.nan
            # findings["cluster_id"] = [np.nan if i > 999 else i for i in findings.cluster_id]

            if 'cluster_id' in findings_fqdn.columns:
                findings_fqdn["cluster_id"] = [np.nan if i > 999 else i for i in findings_fqdn.cluster_id]
            else:
                findings_fqdn["cluster_id"] = np.nan
            # findings_fqdn["cluster_id"] = [np.nan if i > 999 else i for i in findings_fqdn.cluster_id]

            my_logger.info(f"Transforming cluster_id on pc_history ({len(pc_history)}) and pc_history_fqdn ({len(pc_history_fqdn)})")
            pc_history = pc_history.copy()
            if 'cluster_id' in pc_history.columns:
                pc_history["cluster_id"] = [np.nan if i > 999 else i for i in pc_history.cluster_id]
            else:
                pc_history["cluster_id"] = np.nan
            # pc_history["cluster_id"] = [np.nan if i > 999 else i for i in pc_history.cluster_id]
            
            pc_history_fqdn = pc_history_fqdn.copy()
            if 'cluster_id' in pc_history_fqdn.columns:
                pc_history_fqdn["cluster_id"] = [np.nan if i > 999 else i for i in pc_history_fqdn.cluster_id]
            else:
                pc_history_fqdn["cluster_id"] = np.nan

            my_logger.info(f"Filtering and transforming cluster_id on severity_pc_mean ({len(severity_pc_mean)})")
            severity_pc_mean = severity_pc_mean.copy()
            severity_pc_mean = severity_pc_mean[~((severity_pc_mean.cluster_id >= 1000) & (severity_pc_mean.level == "cluster"))]
            severity_pc_mean = severity_pc_mean.copy()
            severity_pc_mean["cluster_id"] = [np.nan if i > 999 else i for i in severity_pc_mean.cluster_id]

            my_logger.info(f"Export replace : findings ({len(findings)}) -> cyber_rating_datafindings")
            self.export_replace_tables(findings, "cyber_rating_datafindings")
            my_logger.info(f"Export replace : findings_assets ({len(findings_assets)}) -> cyber_rating_datafindingsasset")
            self.export_replace_tables(findings_assets, "cyber_rating_datafindingsasset")
            my_logger.info(f"Export replace : findings_fqdn ({len(findings_fqdn)}) -> cyber_rating_datafindingsfqdn")
            self.export_replace_tables(findings_fqdn, "cyber_rating_datafindingsfqdn")
            my_logger.info(f"Export replace : findings_assets_fqdn ({len(findings_assets_fqdn)}) -> cyber_rating_datafindingsassetfqdn")
            self.export_replace_tables(findings_assets_fqdn, "cyber_rating_datafindingsassetfqdn")
            my_logger.info(f"Export replace : pc_history ({len(pc_history)}) -> cyber_rating_patchingcadencenew")
            self.export_replace_tables(pc_history, "cyber_rating_patchingcadencenew")
            my_logger.info(f"Export replace : pc_history_fqdn ({len(pc_history_fqdn)}) -> cyber_rating_patchingcadencenewfqdn")
            self.export_replace_tables(pc_history_fqdn, "cyber_rating_patchingcadencenewfqdn")
            my_logger.info(f"Export replace : severity_pc_mean ({len(severity_pc_mean)}) -> cyber_rating_patchingcadenceseveritymean")
            self.export_replace_tables(severity_pc_mean, "cyber_rating_patchingcadenceseveritymean")

            my_logger.info("[rating_tables_export] End of export of rating tables.")
        except Exception as e:
            my_logger.error(f"[rating_tables_export] Error during export : {str(e)}", exc_info=True)
            raise

    @staticmethod
    def rename_correction_tables(category: pd.DataFrame, family: pd.DataFrame,
                                 ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
        """
        Renames some columns for correction tables

        Parameters
        ----------
        category : DataFrame
            The dataframe with category correction
        family: DataFrame
            The dataframe with family correction
        ip : DataFrame
            The dataframe with ip correction
        ip : DataFrame
            The dataframe with fqdn correction
        """

        category.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        family.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        ip.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

        fqdn.rename(
            columns={"family": "orange_cyber_rating_family_id",
                     "category": "orange_cyber_rating_category_id",
                     "division": "division_id",
                     "cluster": "cluster_id",
                     "subsidiary": "subsidiary_id",
                     "asset": "asset_id",
                     "rating_letter": "rating_letter_id"},
            inplace=True)

    def correction_tables_export(self, category: pd.DataFrame, family: pd.DataFrame,
                                 ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
        """
        Runs all functions to export correction tables

        Parameters
        ----------
        category : DataFrame
            The dataframe with category correction
        family: DataFrame
            The dataframe with family correction
        ip : DataFrame
            The dataframe with ip correction
        fqdn : DataFrame
            The dataframe with fqdn correction
        """

        self.rename_correction_tables(category, family, ip, fqdn)
        self.export_replace_tables(category, "cyber_rating_categorycorrection")
        self.export_replace_tables(family, "cyber_rating_familycorrection")
        self.export_replace_tables(ip, "cyber_rating_ipcorrection")
        self.export_replace_tables(fqdn, "cyber_rating_fqdncorrection")

    def table_write_test(self):
        table_list = ["cyber_rating_orangecyberratinggradecategory",
                      "cyber_rating_orangecyberratinggradefamily",
                      "cyber_rating_orangecyberratinggrade",
                      "cyber_rating_patchingcadencenew",
                      "cyber_rating_patchingcadencenewfqdn",
                      "cyber_rating_categorycorrection",
                      "cyber_rating_familycorrection",
                      "cyber_rating_ipcorrection",
                      "cyber_rating_fqdncorrection"]

        for table_name in table_list:
            try:
                with self.engine.connect() as conn:
                    result = conn.execute(text("SELECT MAX(timestamp) AS max_timestamp FROM {}".format(table_name)))
                    max_date = result.scalar()  # Récupérer la valeur de max_timestamp

                if max_date == date.today():
                    continue
                else:
                    raise ValueError(f"The '{table_name}' table was not loaded today. Last loaded : {max_date}")
            except ValueError as e:
                # Affiche l'erreur mais continue le script
                print(f"Erreur: {e}")


if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)
